import { Injectable, inject, DestroyRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged } from 'rxjs/operators';
import { createLogger } from '../utils/logger';

export interface VisualFeedbackState {
  selectedNodeIds: Set<string>;
  highlightedNodeId: string | null;
  selectionMode: 'single' | 'multi';
  isSelectionActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class UIDesignVisualFeedbackService {

  private readonly destroyRef = inject(DestroyRef);

  // State management with BehaviorSubjects
  private readonly selectedNodeIdsSubject = new BehaviorSubject<Set<string>>(new Set());
  private readonly highlightedNodeIdSubject = new BehaviorSubject<string | null>(null);
  private readonly selectionModeSubject = new BehaviorSubject<'single' | 'multi'>('single');
  private readonly isSelectionActiveSubject = new BehaviorSubject<boolean>(false);

  // Public observables
  public readonly selectedNodeIds$ = this.selectedNodeIdsSubject.asObservable();
  public readonly highlightedNodeId$ = this.highlightedNodeIdSubject.asObservable();
  public readonly selectionMode$ = this.selectionModeSubject.asObservable();
  public readonly isSelectionActive$ = this.isSelectionActiveSubject.asObservable();

  // Combined state observable
  public readonly visualFeedbackState$: Observable<VisualFeedbackState> = combineLatest([
    this.selectedNodeIds$,
    this.highlightedNodeId$,
    this.selectionMode$,
    this.isSelectionActive$
  ]).pipe(
    map(([selectedNodeIds, highlightedNodeId, selectionMode, isSelectionActive]) => ({
      selectedNodeIds,
      highlightedNodeId,
      selectionMode,
      isSelectionActive
    })),
    distinctUntilChanged((prev, curr) =>
      prev.selectedNodeIds.size === curr.selectedNodeIds.size &&
      [...prev.selectedNodeIds].every(id => curr.selectedNodeIds.has(id)) &&
      prev.highlightedNodeId === curr.highlightedNodeId &&
      prev.selectionMode === curr.selectionMode &&
      prev.isSelectionActive === curr.isSelectionActive
    ),
    takeUntilDestroyed(this.destroyRef)
  );

  constructor() {

  }

  /**
   * Check if a node is selected
   */
  isNodeSelected(nodeId: string): boolean {
    return this.selectedNodeIdsSubject.value.has(nodeId);
  }

  /**
   * Check if a node is highlighted
   */
  isNodeHighlighted(nodeId: string): boolean {
    return this.highlightedNodeIdSubject.value === nodeId;
  }

  /**
   * Get current selection count
   */
  getSelectionCount(): number {
    return this.selectedNodeIdsSubject.value.size;
  }

  /**
   * Get all selected node IDs as array
   */
  getSelectedNodeIds(): string[] {
    return Array.from(this.selectedNodeIdsSubject.value);
  }

  /**
   * Select a single node (clears previous selection)
   */
  selectNode(nodeId: string): void {
    const newSelection = new Set([nodeId]);
    this.selectedNodeIdsSubject.next(newSelection);
    this.isSelectionActiveSubject.next(true);
    this.selectionModeSubject.next('single');

  }

  /**
   * Add node to selection (multi-select)
   */
  addNodeToSelection(nodeId: string): void {
    const currentSelection = new Set(this.selectedNodeIdsSubject.value);
    currentSelection.add(nodeId);

    this.selectedNodeIdsSubject.next(currentSelection);
    this.isSelectionActiveSubject.next(true);
    this.selectionModeSubject.next('multi');

  }

  /**
   * Remove node from selection
   */
  removeNodeFromSelection(nodeId: string): void {
    const currentSelection = new Set(this.selectedNodeIdsSubject.value);
    currentSelection.delete(nodeId);

    this.selectedNodeIdsSubject.next(currentSelection);

    if (currentSelection.size === 0) {
      this.isSelectionActiveSubject.next(false);
    }

  }

  /**
   * Toggle node selection
   */
  toggleNodeSelection(nodeId: string, isMultiSelect: boolean = false): void {
    const currentSelection = new Set(this.selectedNodeIdsSubject.value);

    if (currentSelection.has(nodeId)) {
      if (isMultiSelect) {
        this.removeNodeFromSelection(nodeId);
      } else {
        this.clearSelection();
      }
    } else {
      if (isMultiSelect) {
        this.addNodeToSelection(nodeId);
      } else {
        this.selectNode(nodeId);
      }
    }
  }

  /**
   * Select all nodes
   */
  selectAllNodes(nodeIds: string[]): void {
    const newSelection = new Set(nodeIds);
    this.selectedNodeIdsSubject.next(newSelection);
    this.isSelectionActiveSubject.next(true);
    this.selectionModeSubject.next('multi');

  }

  /**
   * Clear all selection
   */
  clearSelection(): void {
    this.selectedNodeIdsSubject.next(new Set());
    this.isSelectionActiveSubject.next(false);
    this.highlightedNodeIdSubject.next(null);

  }

  /**
   * Highlight a node (for hover effects)
   */
  highlightNode(nodeId: string | null): void {
    this.highlightedNodeIdSubject.next(nodeId);
  }

  /**
   * Get visual feedback classes for a node
   */
  getNodeClasses(nodeId: string): { [key: string]: boolean } {
    return {
      'selected-for-editing': this.isNodeSelected(nodeId),
      'highlighted': this.isNodeHighlighted(nodeId),
      'selection-active': this.isSelectionActiveSubject.value
    };
  }

  /**
   * Get enhanced visual feedback for better visibility
   */
  getEnhancedNodeClasses(nodeId: string): { [key: string]: boolean } {
    const isSelected = this.isNodeSelected(nodeId);
    const isHighlighted = this.isNodeHighlighted(nodeId);
    const selectionCount = this.getSelectionCount();

    return {
      'selected-for-editing': isSelected,
      'highlighted': isHighlighted,
      'multi-selected': isSelected && selectionCount > 1,
      'single-selected': isSelected && selectionCount === 1,
      'selection-active': this.isSelectionActiveSubject.value,
      'has-selection': selectionCount > 0
    };
  }

  /**
   * Reset service state
   */
  reset(): void {
    this.clearSelection();
    this.selectionModeSubject.next('single');

  }
}
