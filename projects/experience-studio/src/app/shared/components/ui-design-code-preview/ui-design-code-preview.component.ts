import {
  Component,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  inject,
  DestroyRef,
  ViewChild,
  ElementRef,
  HostListener,
  AfterViewInit
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, combineLatest, of } from 'rxjs';
import { map, catchError, finalize, delay, debounceTime } from 'rxjs/operators';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

import { createLogger } from '../../utils/logger';
import { ThemeService } from '../../services/theme-service/theme.service';
import { GenerateUIDesignService } from '../../services/generate-ui-design.service';
import { ToastService } from '../../services/toast.service';
import { UserSignatureService } from '../../services/user-signature.service';
import { ScreenshotService } from '../../services/screenshot.service';
import { CodeWindowUIDesignStateService } from '../code-window/services/code-window-ui-design-state.service';
import { UIDesignNode } from '../code-window/services/ui-design-node.service';
import { WireframeGenerationStateService, WireframeGenerationState } from '../../services/wireframe-generation-state.service';

// Import new services
import { CanvasService } from './services/canvas.service';
import { NodeManagementService, ScreenNode } from './services/node-management.service';
import { ViewportService } from './services/viewport.service';
import { SelectionService } from './services/selection.service';
import { SafeSrcdocDirective } from '../../directives/safe-srcdoc.directive';

import {
  LeftPanelComponent,
  RightPanelComponent,
  SplitScreenComponent,
} from '@awe/play-comp-library';
import { IconsComponent } from '../icons/icons.component';
import { ChatWindowComponent } from '../chat-window/chat-window.component';
import { LoadingAnimationComponent } from '../code-window/loading-animation/loading-animation.component';

// Interface for UI Design pages from API response
export interface UIDesignPage {
  fileName: string;
  content: string;
}

// Legacy interface for backward compatibility
export interface ScreenBox {
  id: string;
  title: string;
  htmlContent: SafeHtml;
  rawContent: string;
  isLoading: boolean;
}

// View mode for full-screen modal
export type ViewMode = 'mobile' | 'web';

@Component({
  selector: 'app-ui-design-code-preview',
  standalone: true,
  imports: [
    CommonModule,
    SplitScreenComponent,
    LeftPanelComponent,
    RightPanelComponent,
    IconsComponent,
    ChatWindowComponent,
    LoadingAnimationComponent,
    SafeSrcdocDirective,
  ],
  templateUrl: './ui-design-code-preview.component.html',
  styleUrls: ['./ui-design-code-preview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UIDesignCodePreviewComponent implements OnInit, AfterViewInit, OnDestroy {
  private readonly destroyRef = inject(DestroyRef);

  // State management with BehaviorSubjects (simplified for React Flow approach)
  private readonly isLoading$ = new BehaviorSubject<boolean>(true);
  private readonly currentTheme$ = new BehaviorSubject<'light' | 'dark'>('light');
  private readonly isFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private readonly selectedScreenNode$ = new BehaviorSubject<ScreenNode | null>(null);
  private readonly currentViewMode$ = new BehaviorSubject<ViewMode>('mobile');
  private readonly hasError$ = new BehaviorSubject<boolean>(false);
  private readonly errorMessage$ = new BehaviorSubject<string>('');

  // Simple backup nodes for immediate display
  private readonly backupNodes$ = new BehaviorSubject<ScreenNode[]>([]);

  // Wireframe generation state
  wireframeGenerationState: WireframeGenerationState = {
    isGenerating: false,
    isComplete: false,
    hasError: false,
    errorMessage: null,
    nodesCount: 0,
    canShowSelectionControls: false
  };

  // Public observables for template
  readonly isLoading = this.isLoading$.asObservable();
  readonly currentTheme = this.currentTheme$.asObservable();
  readonly isFullScreenOpen = this.isFullScreenOpen$.asObservable();
  readonly selectedScreenNode = this.selectedScreenNode$.asObservable();
  readonly currentViewMode = this.currentViewMode$.asObservable();
  readonly hasError = this.hasError$.asObservable();
  readonly errorMessage = this.errorMessage$.asObservable();

  // ViewChild references
  @ViewChild('infiniteCanvas', { static: false }) infiniteCanvas!: ElementRef<HTMLDivElement>;
  @ViewChild('canvasContainer', { static: false }) canvasContainer!: ElementRef<HTMLDivElement>;
  @ViewChild('fullScreenModal', { static: false }) fullScreenModal!: ElementRef<HTMLDivElement>;
  @ViewChild(ChatWindowComponent) chatWindow!: ChatWindowComponent;

  // Chat window properties
  lightMessages: {
    text: string;
    from: 'user' | 'ai';
    theme: 'light';
    hasSteps?: boolean;
    imageDataUri?: string;
    isError?: boolean;
    errorDetails?: string;
    isErrorExpanded?: boolean;
  }[] = [];

  lightPrompt: string = '';
  rightIcons: { name: string; status: 'default' | 'active' | 'disable' }[] = [];

  // Loading messages for UI Design generation
  loadingMessages = [
    'Analyzing your UI design requirements... 🎨',
    'Creating responsive design components... 📱',
    'Generating mobile-first layouts... 📲',
    'Building interactive UI elements... 🖱️',
    'Implementing design system patterns... 🎯',
    'Optimizing for multiple screen sizes... 📐',
    'Adding accessibility features... ♿',
    'Finalizing your UI design pages... ✨',
    'Preparing preview deployment... 🚀',
  ];

  constructor(
    private readonly themeService: ThemeService,
    private readonly generateUIDesignService: GenerateUIDesignService,
    private readonly router: Router,
    private readonly sanitizer: DomSanitizer,
    private readonly cdr: ChangeDetectorRef,
    private readonly canvasService: CanvasService,
    private readonly nodeManagementService: NodeManagementService,
    private readonly viewportService: ViewportService,
    private readonly selectionService: SelectionService,
    private readonly screenshotService: ScreenshotService,
    private readonly uiDesignStateService: CodeWindowUIDesignStateService,
    private readonly wireframeGenerationStateService: WireframeGenerationStateService
  ) {
    // Initialize theme
    this.currentTheme$.next(this.themeService.getCurrentTheme());
  }

  ngOnInit(): void {

    this.initializeComponent();
    this.subscribeToThemeChanges();
    this.subscribeToUIDesignNodes();
    this.subscribeToWireframeGenerationState();

    // Load data immediately for testing
    this.loadSimpleFallbackData();

    // Also load via the normal flow
    this.loadUIDesignData();
  }

  ngAfterViewInit(): void {

    this.initializeCanvas();

    // Force initial data load if not already loaded
    setTimeout(() => {
      if (this.nodeManagementService.getNodes().length === 0) {

        this.loadSimpleFallbackData();
      }
    }, 100);
  }

  ngOnDestroy(): void {

    this.nodeManagementService.clearAll();
  }

  /**
   * Initialize component state and setup
   */
  private initializeComponent(): void {
    // Initialize chat messages
    this.initializeChatMessages();

    // Set initial loading state
    this.isLoading$.next(true);
    this.hasError$.next(false);
  }

  /**
   * Initialize React Flow-inspired canvas
   */
  private initializeCanvas(): void {

    // Initialize viewport service with container size
    if (this.canvasContainer?.nativeElement) {
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      this.viewportService.updateContainerSize(rect.width, rect.height);
    }

    // Subscribe to window resize events
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }

  /**
   * Handle window resize
   */
  private onWindowResize(): void {
    if (this.canvasContainer?.nativeElement) {
      const rect = this.canvasContainer.nativeElement.getBoundingClientRect();
      this.viewportService.updateContainerSize(rect.width, rect.height);
    }
  }

  /**
   * Subscribe to theme changes
   */
  private subscribeToThemeChanges(): void {
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((theme: 'light' | 'dark') => {
        this.currentTheme$.next(theme);
        // OnPush: BehaviorSubject update automatically triggers change detection via async pipe
      });
  }

  /**
   * Subscribe to UI Design nodes changes for debugging
   */
  private subscribeToUIDesignNodes(): void {
    this.uiDesignStateService.uiDesignNodes
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((nodes: UIDesignNode[]) => {

        // Force change detection when nodes update
        this.cdr.detectChanges();

        // Update loading state
        if (nodes.length > 0) {
          this.isLoading$.next(false);
          this.hasError$.next(false);
        }
      });
  }

  /**
   * Subscribe to wireframe generation state changes
   */
  private subscribeToWireframeGenerationState(): void {
    this.wireframeGenerationStateService.wireframeGenerationState$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((state: WireframeGenerationState) => {
        this.wireframeGenerationState = state;

        // Force change detection when state updates
        this.cdr.detectChanges();
      });
  }

  /**
   * Initialize chat messages with UI Design context
   */
  private initializeChatMessages(): void {
    const uiDesignData = this.generateUIDesignService.getUIDesignData();

    if (uiDesignData) {
      this.lightMessages = [
        {
          text: `Creating UI design for: "${uiDesignData.prompt}"`,
          from: 'user',
          theme: 'light'
        },
        {
          text: `Generating ${uiDesignData.applicationTarget} application UI design...`,
          from: 'ai',
          theme: 'light',
          hasSteps: true
        }
      ];
    } else {
      this.lightMessages = [
        {
          text: 'Generating UI design...',
          from: 'ai',
          theme: 'light',
          hasSteps: true
        }
      ];
    }
  }

  /**
   * Load UI Design data from API or fallback
   */
  loadUIDesignData(): void {

    // Simulate API call delay
    of(null)
      .pipe(
        delay(1000), // Reduced delay for faster testing
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        // For now, use simple fallback data
        // In real implementation, this would call the API
        this.loadSimpleFallbackData();
      });
  }

  /**
   * Load fallback sample data
   */
  private loadFallbackData(): void {

    const fallbackPages: UIDesignPage[] = [
      {
        fileName: 'Landing Page',
        content: this.createSampleHTML('Landing Page', 'Welcome to our amazing product', '#667eea')
      },
      {
        fileName: 'About Us',
        content: this.createSampleHTML('About Us', 'Learn more about our company', '#764ba2')
      },
      {
        fileName: 'Services',
        content: this.createSampleHTML('Services', 'Discover our premium services', '#4facfe')
      },
      {
        fileName: 'Contact',
        content: this.createSampleHTML('Contact', 'Get in touch with us today', '#f093fb')
      },
      {
        fileName: 'Portfolio',
        content: this.createSampleHTML('Portfolio', 'View our latest work', '#a8edea')
      }
    ];

    this.processUIDesignPages(fallbackPages);
  }

  /**
   * Simple fallback method for immediate display
   */
  private loadSimpleFallbackData(): void {

    try {
      // Clear existing nodes first
      this.nodeManagementService.clearAll();

      // Create simple nodes
      const simplePages = [
        { title: 'Landing Page', color: '#667eea' },
        { title: 'About Us', color: '#764ba2' },
        { title: 'Services', color: '#4facfe' },
        { title: 'Contact', color: '#f093fb' },
        { title: 'Portfolio', color: '#a8edea' }
      ];

      simplePages.forEach((page, index) => {
        const htmlContent = this.createSampleHTML(page.title, `Sample ${page.title} content`, page.color);
        const decodedContent = this.decodeHtmlEntities(htmlContent);
        const processedContent = this.ensureProperHtmlStyling(decodedContent);

        const node: ScreenNode = {
          id: `node-${Date.now()}-${index}`,
          type: 'screen',
          data: {
            title: page.title,
            htmlContent: this.sanitizer.bypassSecurityTrustHtml(processedContent),
            rawContent: processedContent,
            width: 420,
            height: 720,
            isLoading: false
          },
          position: {
            x: (index % 2) * 480 - 240, // 2 columns for larger nodes
            y: Math.floor(index / 2) * 780 - 390
          },
          selected: false,
          dragging: false,
          visible: true
        };

        this.nodeManagementService.addNode(node);
      });

      // Verify nodes were added
      const addedNodes = this.nodeManagementService.getNodes();

      // Also populate backup nodes for immediate display
      const backupNodes: ScreenNode[] = simplePages.map((page, index) => {
        const htmlContent = this.createSampleHTML(page.title, `Sample ${page.title} content`, page.color);
        const decodedContent = this.decodeHtmlEntities(htmlContent);
        const processedContent = this.ensureProperHtmlStyling(decodedContent);

        return {
          id: `backup-node-${index}`,
          type: 'screen',
          data: {
            title: page.title,
            htmlContent: this.sanitizer.bypassSecurityTrustHtml(processedContent),
            rawContent: processedContent,
            width: 420,
            height: 720,
            isLoading: false
          },
          position: {
            x: (index % 2) * 480 - 240,
            y: Math.floor(index / 2) * 780 - 390
          },
          selected: false,
          dragging: false,
          visible: true
        };
      });

      this.backupNodes$.next(backupNodes);

      this.isLoading$.next(false);
      this.hasError$.next(false);
      this.updateChatMessagesOnSuccess(simplePages.length);
      // OnPush: BehaviorSubject updates automatically trigger change detection via async pipe

    } catch (error) {

      this.hasError$.next(true);
      this.errorMessage$.next('Failed to load UI design data');
    }
  }

  /**
   * Process UI Design pages and create screen nodes (React Flow inspired)
   */
  private processUIDesignPages(pages: UIDesignPage[]): void {

    // Clear existing nodes
    this.nodeManagementService.clearAll();

    // Calculate optimal positions for nodes
    const positions = this.nodeManagementService.calculateOptimalLayout(pages.length);

    // Create screen nodes with smaller default size (React Flow inspired)
    pages.forEach((page, index) => {
      const decodedContent = this.decodeHtmlEntities(page.content);
      const processedContent = this.ensureProperHtmlStyling(decodedContent);
      const node: ScreenNode = {
        id: this.nodeManagementService.generateNodeId(),
        type: 'screen',
        data: {
          title: page.fileName,
          htmlContent: this.sanitizer.bypassSecurityTrustHtml(processedContent),
          rawContent: processedContent,
          width: 420, // Mobile device width
          height: 720, // Mobile device height
          isLoading: false
        },
        position: positions[index] || { x: 0, y: 0 },
        selected: false,
        dragging: false,
        visible: true
      };

      this.nodeManagementService.addNode(node);
    });

    this.isLoading$.next(false);
    this.hasError$.next(false);

    // Update chat messages
    this.updateChatMessagesOnSuccess(pages.length);
    // OnPush: BehaviorSubject updates automatically trigger change detection via async pipe
  }

  /**
   * Decode HTML entities to prevent double encoding in iframe
   */
  private decodeHtmlEntities(content: string): string {
    if (!content) return '';

    // Create a temporary DOM element to decode HTML entities
    const textarea = document.createElement('textarea');
    textarea.innerHTML = content;
    let decodedContent = textarea.value;

    // Additional manual decoding for common entities that might not be handled
    decodedContent = decodedContent
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .replace(/&#10;/g, '\n')
      .replace(/&nbsp;/g, ' ');

    return decodedContent;
  }

  /**
   * Ensure HTML content has proper styling for iframe rendering
   */
  private ensureProperHtmlStyling(content: string): string {
    if (!content) return '';

    // Check if content already has a complete HTML structure
    if (content.includes('<!DOCTYPE html>') || content.includes('<html>')) {
      return content;
    }

    // Check if content has basic CSS reset
    const hasBasicStyling = content.includes('margin:') || content.includes('padding:') || content.includes('<style>');

    if (hasBasicStyling) {
      return content;
    }

    // Super responsive CSS for canvas rendering with hidden scrollbars but scroll enabled
    const basicCSS = `
      <style>
        * {
          box-sizing: border-box;
        }
        html {
          /* Hide scrollbar for Chrome, Safari and Opera */
          overflow: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        html::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
        body {
          margin: 0;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #333;
          background: white;
          font-size: clamp(12px, 3.5vw, 16px);
          /* Enable scrolling but hide scrollbars */
          overflow: auto;
          scrollbar-width: none; /* Firefox */
          -ms-overflow-style: none; /* Internet Explorer 10+ */
        }
        body::-webkit-scrollbar {
          display: none; /* Chrome, Safari and Opera */
        }
        /* Super responsive typography */
        h1 { font-size: clamp(20px, 6vw, 32px); margin: 0.5em 0; }
        h2 { font-size: clamp(18px, 5vw, 28px); margin: 0.5em 0; }
        h3 { font-size: clamp(16px, 4.5vw, 24px); margin: 0.5em 0; }
        h4 { font-size: clamp(14px, 4vw, 20px); margin: 0.5em 0; }
        h5 { font-size: clamp(13px, 3.5vw, 18px); margin: 0.5em 0; }
        h6 { font-size: clamp(12px, 3vw, 16px); margin: 0.5em 0; }
        p { font-size: clamp(12px, 3.5vw, 16px); margin: 0.5em 0; }
        /* Responsive images and media */
        img, video, iframe, embed, object {
          max-width: 100%;
          height: auto;
          display: block;
          margin: 0 auto;
        }
        /* Responsive buttons */
        button, .btn, input[type="submit"], input[type="button"] {
          padding: 0.75em 1.5em;
          font-size: clamp(12px, 3.5vw, 16px);
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
          cursor: pointer;
          min-height: 44px; /* Touch-friendly */
        }
        button:hover, .btn:hover {
          background: #f5f5f5;
        }
        /* Responsive forms */
        input, textarea, select {
          width: 100%;
          padding: 0.75em;
          font-size: clamp(12px, 3.5vw, 16px);
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 1em;
        }
      </style>
    `;

    // If content is just a fragment, wrap it in a basic HTML structure
    if (!content.includes('<body>')) {
      return `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  ${basicCSS}
</head>
<body>
  ${content}
</body>
</html>`;
    }

    // If content has body but no head styling, inject CSS into head or body
    if (content.includes('<head>')) {
      return content.replace('<head>', `<head>${basicCSS}`);
    } else if (content.includes('<body>')) {
      return content.replace('<body>', `<body>${basicCSS}`);
    }

    return content;
  }

  /**
   * Create sample HTML content for fallback
   */
  private createSampleHTML(title: string, description: string, primaryColor: string): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, ${primaryColor}20, ${primaryColor}10);
            min-height: 100vh;
          }
          .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            background: linear-gradient(135deg, ${primaryColor}, ${primaryColor}dd);
            color: white;
            padding: 60px 20px;
            text-align: center;
            border-radius: 12px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
          }
          .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
          }
          .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
          }
          .content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
          }
          .card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
          }
          .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
          }
          .card h3 {
            color: ${primaryColor};
            margin-bottom: 15px;
            font-size: 1.5rem;
          }
          .card p {
            color: #666;
            line-height: 1.8;
          }
          .cta-section {
            background: white;
            padding: 50px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
          }
          .cta-button {
            background: linear-gradient(135deg, ${primaryColor}, ${primaryColor}dd);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
          }
          .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
          }
          @media (max-width: 768px) {
            .header h1 { font-size: 2rem; }
            .header p { font-size: 1rem; }
            .container { padding: 15px; }
            .content { grid-template-columns: 1fr; gap: 20px; }
            .card, .cta-section { padding: 25px; }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${title}</h1>
            <p>${description}</p>
          </div>

          <div class="content">
            <div class="card">
              <h3>Feature One</h3>
              <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
            </div>
            <div class="card">
              <h3>Feature Two</h3>
              <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            </div>
            <div class="card">
              <h3>Feature Three</h3>
              <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
            </div>
          </div>

          <div class="cta-section">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of satisfied customers who trust our platform.</p>
            <a href="#" class="cta-button">Get Started Today</a>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Update chat messages on successful generation
   */
  private updateChatMessagesOnSuccess(pageCount: number): void {
    this.lightMessages = [
      ...this.lightMessages.slice(0, -1), // Remove the last loading message
      {
        text: `Successfully generated ${pageCount} UI design pages! Double-click any screen to view in full-screen mode.`,
        from: 'ai',
        theme: 'light'
      }
    ];
  }

  /**
   * Handle double-click on screen node
   */
  onNodeDoubleClick(node: ScreenNode): void {

    // Set the viewport mode to match the current application target from prompt bar
    const currentApplicationTarget = this.generateUIDesignService.getApplicationTarget();
    const viewMode: ViewMode = currentApplicationTarget === 'web' ? 'web' : 'mobile';

    this.currentViewMode$.next(viewMode);
    this.selectedScreenNode$.next(node);
    this.isFullScreenOpen$.next(true);
    // OnPush: BehaviorSubject updates automatically trigger change detection via async pipe
  }

  /**
   * Handle single click on screen node
   */
  onNodeClick(node: ScreenNode, event: MouseEvent): void {
    const clickType = this.selectionService.handleNodeClick(node.id, event);
    if (clickType === 'double') {
      this.onNodeDoubleClick(node);
    }
  }

  /**
   * Close full-screen modal
   */
  closeFullScreen(): void {
    this.isFullScreenOpen$.next(false);
    this.selectedScreenNode$.next(null);
    // OnPush: BehaviorSubject updates automatically trigger change detection via async pipe
  }

  /**
   * Switch view mode in full-screen
   */
  switchViewMode(mode: ViewMode): void {
    this.currentViewMode$.next(mode);
    // OnPush: BehaviorSubject updates automatically trigger change detection via async pipe
  }

  /**
   * Open selected node in new tab
   */
  openSelectedNodeInNewTab(): void {
    const selectedNode = this.selectedScreenNode$.value;
    if (!selectedNode?.data?.rawContent) {

      return;
    }

    try {
      // Create a new window/tab
      const newWindow = window.open('', '_blank');

      if (newWindow) {
        // Write the HTML content directly to the new window
        newWindow.document.write(selectedNode.data.rawContent);
        newWindow.document.close();

        // Set the page title
        const title = selectedNode.data.title || 'Wireframe Preview';
        newWindow.document.title = title;

      } else {
        // Fallback: use blob URL if popup is blocked
        this.openWithBlobUrl(selectedNode.data.rawContent);
      }
    } catch (error) {

      // Fallback: use blob URL
      this.openWithBlobUrl(selectedNode.data.rawContent);
    }
  }

  private openWithBlobUrl(content: string): void {
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    window.open(url, '_blank');

    // Clean up the blob URL after a short delay
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 1000);
  }

  /**
   * Download screenshot of selected node in fullscreen modal
   */
  downloadSelectedNodeScreenshot(): void {
    const selectedNode = this.selectedScreenNode$.value;
    if (!selectedNode) {

      return;
    }

    // Find the fullscreen iframe element
    const fullscreenIframe = document.querySelector('.fullscreen-iframe') as HTMLIFrameElement;

    if (!fullscreenIframe) {

      return;
    }

    // Get current viewport mode and use appropriate screenshot method
    const currentViewMode = this.currentViewMode$.value;
    const currentApplicationTarget = this.generateUIDesignService.getApplicationTarget();

    // Use the viewport mode from the modal, but fallback to application target if needed
    const viewportMode = currentViewMode || (currentApplicationTarget === 'web' ? 'web' : 'mobile');

    const filename = `${selectedNode.data.title || 'wireframe'}-${viewportMode}`;

    this.screenshotService.captureViewportAwareScreenshot(
      viewportMode,
      viewportMode === 'mobile' ? fullscreenIframe : undefined,
      viewportMode === 'web' ? fullscreenIframe : undefined,
      filename
    ).subscribe({
      next: () => {

      },
      error: (error) => {

      }
    });
  }

  /**
   * Handle escape key to close modal
   */
  @HostListener('document:keydown.escape', ['$event'])
  onEscapeKey(event: KeyboardEvent): void {
    if (this.isFullScreenOpen$.value) {
      this.closeFullScreen();
    }
  }

  /**
   * Navigate to home
   */
  navigateToHome(): void {
    this.router.navigate(['/']);
  }

  /**
   * Handle chat window interactions
   */
  handleIconClick(event: any): void {

  }

  handleEnhancedSendLight(): void {

    // Handle regeneration or additional prompts
  }

  /**
   * Get canvas transform style for React Flow-inspired rendering
   */
  getCanvasTransformStyle(): string {
    return this.canvasService.getTransformStyle();
  }

  /**
   * Get current zoom percentage
   */
  getZoomPercentage(): number {
    return this.canvasService.getZoomPercentage();
  }

  /**
   * Handle canvas mouse down for panning
   */
  onCanvasMouseDown(event: MouseEvent): void {
    if (event.target === this.canvasContainer?.nativeElement) {
      this.canvasService.startDragging(event.clientX, event.clientY);
      event.preventDefault();
    }
  }

  /**
   * Handle canvas mouse move for panning
   */
  @HostListener('document:mousemove', ['$event'])
  onCanvasMouseMove(event: MouseEvent): void {
    const viewport = this.canvasService.getViewport();
    if (viewport.isDragging) {
      const deltaX = event.clientX - viewport.lastMouseX;
      const deltaY = event.clientY - viewport.lastMouseY;
      this.canvasService.panCanvas(deltaX, deltaY);
      this.canvasService.updateViewport({
        lastMouseX: event.clientX,
        lastMouseY: event.clientY
      });
    }
  }

  /**
   * Handle canvas mouse up to stop panning
   */
  @HostListener('document:mouseup', ['$event'])
  onCanvasMouseUp(event: MouseEvent): void {
    this.canvasService.stopDragging();
  }

  /**
   * Handle mouse wheel for zooming
   */
  @HostListener('wheel', ['$event'])
  onCanvasWheel(event: WheelEvent): void {
    if (!this.canvasContainer?.nativeElement) return;

    const canvasRect = this.canvasContainer.nativeElement.getBoundingClientRect();
    const isOverCanvas = event.clientX >= canvasRect.left &&
                        event.clientX <= canvasRect.right &&
                        event.clientY >= canvasRect.top &&
                        event.clientY <= canvasRect.bottom;

    if (!isOverCanvas) return;

    event.preventDefault();

    const viewport = this.canvasService.getViewport();
    const zoomDelta = -event.deltaY * 0.001;
    const newZoom = viewport.zoom * (1 + zoomDelta);

    const centerPoint = {
      x: event.clientX - canvasRect.left,
      y: event.clientY - canvasRect.top
    };

    this.canvasService.zoomToLevel(newZoom, centerPoint);
  }

  /**
   * Zoom in
   */
  zoomIn(): void {
    const viewport = this.canvasService.getViewport();
    this.canvasService.zoomToLevel(viewport.zoom * 1.2);
  }

  /**
   * Zoom out
   */
  zoomOut(): void {
    const viewport = this.canvasService.getViewport();
    this.canvasService.zoomToLevel(viewport.zoom * 0.8);
  }

  /**
   * Reset canvas view
   */
  resetCanvasView(): void {
    this.canvasService.resetViewport();
  }

  /**
   * Fit all nodes to view
   */
  fitToView(): void {
    this.viewportService.fitContentToView();
  }

  /**
   * Toggle minimap
   */
  toggleMiniMap(): void {
    this.viewportService.toggleMinimap();
  }

  /**
   * Get nodes observable for template with fallback
   * Converts UIDesignNode[] to ScreenNode[] format expected by template
   */
  get nodes$() {
    // Get nodes from UI Design state service (where wireframe generation updates them)
    return this.uiDesignStateService.uiDesignNodes.pipe(
      map(uiDesignNodes => {
        if (uiDesignNodes && uiDesignNodes.length > 0) {
          // Convert UIDesignNode[] to ScreenNode[] format
          return this.convertUIDesignNodesToScreenNodes(uiDesignNodes);
        }

        // Fallback to local node management service
        const localNodes = this.nodeManagementService.getNodes();
        if (localNodes && localNodes.length > 0) {
          return localNodes;
        }

        // Final fallback to backup nodes
        return this.backupNodes$.value;
      })
    );
  }

  /**
   * Convert UIDesignNode[] to ScreenNode[] format
   */
  private convertUIDesignNodesToScreenNodes(uiDesignNodes: UIDesignNode[]): ScreenNode[] {
    return uiDesignNodes.map(uiNode => {
      const screenNode: ScreenNode = {
        id: uiNode.id,
        type: 'screen',
        data: {
          title: uiNode.data.displayTitle || uiNode.data.title,
          htmlContent: uiNode.data.htmlContent,
          rawContent: uiNode.data.rawContent,
          width: uiNode.data.width,
          height: uiNode.data.height,
          isLoading: uiNode.data.isLoading,
          thumbnail: undefined // UIDesignNode doesn't have thumbnail
        },
        position: {
          x: uiNode.position.x,
          y: uiNode.position.y
        },
        selected: uiNode.selected,
        dragging: uiNode.dragging,
        visible: uiNode.visible
      };
      return screenNode;
    });
  }

  /**
   * Get canvas viewport observable
   */
  get canvasViewport$() {
    return this.canvasService.viewport$;
  }

  /**
   * Get viewport stats observable
   */
  get viewportStats$() {
    return this.viewportService.viewportStats$;
  }

  /**
   * Get minimap config observable
   */
  get minimapConfig$() {
    return this.viewportService.minimapConfig$;
  }

  /**
   * Debug methods for tracking node counts
   */
  getUIDesignNodesCount(): number {
    return this.uiDesignStateService.getUIDesignNodes().length;
  }

  getLocalNodesCount(): number {
    return this.nodeManagementService.getNodes().length;
  }

  getBackupNodesCount(): number {
    return this.backupNodes$.value.length;
  }

  /**
   * Track by function for screen nodes
   */
  trackByNode(_index: number, item: ScreenNode): string {
    return item.id;
  }

  /**
   * Legacy track by function for backward compatibility
   */
  trackByScreenBox(_index: number, item: ScreenBox): string {
    return item.id;
  }

  /**
   * Handle download click for canvas nodes
   */
  onNodeDownloadClick(node: ScreenNode, event: Event): void {
    event.stopPropagation(); // Prevent node selection

    // Find the iframe element for this specific node
    const selector = `[data-node-id="${node.id}"] iframe`;
    const nodeElement = document.querySelector(selector) as HTMLIFrameElement;

    if (!nodeElement) {

      // Try alternative selectors
      const alternativeElement = document.querySelector(`[data-node-id="${node.id}"] .preview-iframe`) as HTMLIFrameElement;
      if (alternativeElement) {

        this.captureNodeScreenshot(alternativeElement, node);
        return;
      }

      return;
    }

    this.captureNodeScreenshot(nodeElement, node);
  }

  private captureNodeScreenshot(iframe: HTMLIFrameElement, node: ScreenNode): void {

    const filename = this.screenshotService.generateFilename(node.data.title);
    const options = this.screenshotService.getCanvasNodeOptions();

    this.screenshotService.captureAndDownloadIframe(iframe, filename, options).subscribe({
      next: () => {

      },
      error: (error) => {

      }
    });
  }
}
